# 慧习作登录页面

基于Figma设计稿1:1还原的登录页面系统。

## 项目结构

```
├── index.html                  # 主登录页面
├── phone-code-login.html      # 手机验证码登录页面
├── phone-password-login.html  # 手机密码登录页面
├── role-selection.html        # 身份选择页面
├── assets/                    # 静态资源文件夹
│   ├── logo.svg              # 慧习作Logo
│   ├── wechat-icon.svg       # 微信图标
│   ├── phone-icon-*.svg      # 手机相关图标
│   ├── lock-icon-*.svg       # 锁图标
│   ├── teacher-icon-*.svg    # 教师图标
│   └── parent-icon-*.svg     # 家长图标
└── README.md                 # 项目说明
```

## 功能特性

### 主登录页面 (index.html)
- ✅ 1:1还原Figma设计稿
- ✅ 三种登录方式：微信授权、手机验证码、手机密码
- ✅ 响应式按钮交互效果
- ✅ 页面跳转功能
- ✅ 加载动画效果

### 手机验证码登录页面 (phone-code-login.html)
- ✅ 手机号码输入验证
- ✅ 验证码发送功能（60秒倒计时）
- ✅ 表单验证
- ✅ 返回按钮
- ✅ 登录功能

### 手机密码登录页面 (phone-password-login.html)
- ✅ 手机号码输入验证
- ✅ 密码输入（支持显示/隐藏切换）
- ✅ 忘记密码链接
- ✅ 注册链接
- ✅ 表单验证
- ✅ 返回按钮

### 身份选择页面 (role-selection.html)
- ✅ 教师/家长身份选择
- ✅ 选择状态可视化
- ✅ 身份信息本地存储
- ✅ 确认选择功能
- ✅ 返回按钮

## 技术实现

- **HTML5** - 语义化标签结构
- **CSS3** - 精确像素级样式还原
- **JavaScript** - 交互功能和表单验证
- **SVG图标** - 矢量图标支持

## 设计规范

- 页面尺寸：390x844px（移动端标准）
- 字体：Inter、Archivo
- 颜色方案：
  - 微信登录：#379AE6
  - 验证码登录：#636AE8
  - 密码登录：#7F55E0
  - 文字颜色：#171A1F、#9095A0、#424955

## 使用方法

1. 直接在浏览器中打开 `index.html`
2. 点击不同的登录按钮体验页面跳转
3. 在登录页面中测试表单验证功能

## 页面流程

1. **index.html** → 选择登录方式
2. **phone-code-login.html** / **phone-password-login.html** → 完成登录
3. **role-selection.html** → 选择身份（教师/家长）

## 编译状态

✅ 项目已成功编译
✅ 所有HTML文件结构完整（4个页面）
✅ 静态资源文件已下载（16个SVG图标）
✅ JavaScript交互功能已实现
✅ 页面跳转逻辑完整
✅ 用户身份管理功能完成
