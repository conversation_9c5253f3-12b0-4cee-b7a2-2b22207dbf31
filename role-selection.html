<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 身份选择</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: transparent;
            border: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bar {
            width: 3px;
            background: #171A1F;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #171A1F;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #171A1F;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #171A1F;
            border-radius: 1px;
        }

        .back-button {
            position: absolute;
            top: 52px;
            left: 33px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button svg {
            width: 18px;
            height: 12px;
            stroke: #171A1F;
            stroke-width: 2px;
        }

        .title {
            position: absolute;
            top: 173px;
            left: 33px;
            font-family: 'Archivo', sans-serif;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            color: #9095A0;
        }

        .role-buttons {
            position: absolute;
            top: 336px;
            left: 71px;
            width: 248px;
            display: flex;
            flex-direction: column;
            gap: 31px;
        }

        .role-button {
            width: 248px;
            height: 52px;
            border-radius: 26px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .role-button.teacher {
            background: #379AE6;
            color: #FFFFFF;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .role-button.parent {
            background: #FFFFFF;
            color: #379AE6;
            border: 1px solid #379AE6;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .role-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(55, 154, 230, 0.2);
        }

        .role-button.selected {
            background: #379AE6;
            color: #FFFFFF;
            border: 1px solid transparent;
        }

        .role-button.unselected {
            background: #FFFFFF;
            color: #379AE6;
            border: 1px solid #379AE6;
        }

        .role-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .role-icon img {
            width: 24px;
            height: 24px;
        }

        .confirm-button {
            position: absolute;
            top: 746px;
            left: 20px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: 1px solid transparent;
            border-radius: 16px;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .confirm-button:hover {
            transform: translateY(-1px);
            box-shadow: 0px 6px 12px 0px rgba(99, 106, 232, 0.2);
        }

        .confirm-button:disabled {
            background: #BCC1CA;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }


    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
        </div>
        <div class="status-right">
            <div class="battery">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>

    <!-- 标题 -->
    <div class="title">请选择身份</div>

    <!-- 身份选择按钮 -->
    <div class="role-buttons">
        <button class="role-button teacher selected" data-role="teacher" onclick="selectRole('teacher')">
            <div class="role-icon">
                <img src="assets/teacher-face.svg" alt="教师头像">
            </div>
            <span>教师</span>
        </button>

        <button class="role-button parent unselected" data-role="parent" onclick="selectRole('parent')">
            <div class="role-icon">
                <img src="assets/parent-face.svg" alt="家长头像">
            </div>
            <span>家长</span>
        </button>
    </div>

    <!-- 确定按钮 -->
    <button class="confirm-button" onclick="confirmSelection()">确定</button>

    <script>
        let selectedRole = 'teacher'; // 默认选择教师

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 选择身份
        function selectRole(role) {
            selectedRole = role;
            
            // 更新按钮状态
            const buttons = document.querySelectorAll('.role-button');
            buttons.forEach(button => {
                const buttonRole = button.getAttribute('data-role');
                if (buttonRole === role) {
                    button.classList.remove('unselected');
                    button.classList.add('selected');
                } else {
                    button.classList.remove('selected');
                    button.classList.add('unselected');
                }
            });

            // 添加点击动画效果
            const clickedButton = document.querySelector(`[data-role="${role}"]`);
            clickedButton.style.transform = 'scale(0.98)';
            setTimeout(() => {
                clickedButton.style.transform = '';
            }, 150);
        }

        // 确认选择
        function confirmSelection() {
            const confirmBtn = document.querySelector('.confirm-button');
            
            // 添加点击动画效果
            confirmBtn.style.transform = 'scale(0.98)';
            setTimeout(() => {
                confirmBtn.style.transform = '';
            }, 150);

            // 保存选择的身份到本地存储
            localStorage.setItem('selectedRole', selectedRole);
            
            // 身份选择完成
            setTimeout(() => {
                if (selectedRole === 'teacher') {
                    alert('教师身份已选择完成');
                } else {
                    alert('家长身份已选择完成');
                }
            }, 300);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有之前保存的身份选择
            const savedRole = localStorage.getItem('selectedRole');
            if (savedRole) {
                selectRole(savedRole);
            }
            
            // 页面加载动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
